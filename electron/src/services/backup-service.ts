import fs from 'fs/promises';
import path from 'path';
import { app } from 'electron';

export interface BackupMetadata {
  timestamp: number;
  date: string;
  dbName: string;
  size: number;
  docCount: number;
}

export interface BackupContext {
  lastBackupTime: number;
  lastCheckTime: number;
  consecutiveFailures: number;
}

export class BackupService {
  private backupDir: string;
  private contextFile: string;
  private couchUrl = '*********************************';

  constructor() {
    const userDataPath = app.getPath('userData');
    this.backupDir = path.join(userDataPath, 'backups');
    this.contextFile = path.join(this.backupDir, 'backup-context.json');
  }

  async ensureBackupDir(): Promise<void> {
    try {
      await fs.access(this.backupDir);
    } catch {
      await fs.mkdir(this.backupDir, { recursive: true });
    }
  }

  async getContext(): Promise<BackupContext> {
    try {
      const data = await fs.readFile(this.contextFile, 'utf8');
      return JSON.parse(data);
    } catch {
      return {
        lastBackupTime: 0,
        lastCheckTime: 0,
        consecutiveFailures: 0
      };
    }
  }

  async saveContext(context: BackupContext): Promise<void> {
    await this.ensureBackupDir();
    await fs.writeFile(this.contextFile, JSON.stringify(context, null, 2));
  }

  async isBackupNeeded(): Promise<boolean> {
    const context = await this.getContext();
    const now = Date.now();
    const twentyFourHours = 24 * 60 * 60 * 1000;
    
    return (now - context.lastBackupTime) > twentyFourHours;
  }

  async createBackup(restaurantId: string): Promise<{ success: boolean; error?: string; metadata?: BackupMetadata }> {
    try {
      await this.ensureBackupDir();
      
      const dbName = `resto-${restaurantId}`;
      const timestamp = Date.now();
      const dateStr = new Date(timestamp).toISOString().split('T')[0];
      const backupFileName = `${dbName}_${dateStr}_${timestamp}.json`;
      const backupPath = path.join(this.backupDir, backupFileName);

      // Fetch all docs from CouchDB
      const response = await fetch(`${this.couchUrl}/${dbName}/_all_docs?include_docs=true`);
      
      if (!response.ok) {
        throw new Error(`CouchDB error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      // Save backup file
      await fs.writeFile(backupPath, JSON.stringify(data, null, 2));
      
      // Get file size
      const stats = await fs.stat(backupPath);
      
      const metadata: BackupMetadata = {
        timestamp,
        date: dateStr,
        dbName,
        size: stats.size,
        docCount: data.rows?.length || 0
      };

      // Update context
      const context = await this.getContext();
      context.lastBackupTime = timestamp;
      context.lastCheckTime = timestamp;
      context.consecutiveFailures = 0;
      await this.saveContext(context);

      console.log(`✅ Backup created: ${backupFileName} (${stats.size} bytes, ${metadata.docCount} docs)`);
      
      return { success: true, metadata };

    } catch (error) {
      console.error('❌ Backup failed:', error);
      
      // Update failure count
      const context = await this.getContext();
      context.consecutiveFailures++;
      context.lastCheckTime = Date.now();
      await this.saveContext(context);

      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  async listLocalBackups(): Promise<BackupMetadata[]> {
    try {
      await this.ensureBackupDir();
      const files = await fs.readdir(this.backupDir);
      const backupFiles = files.filter(f => f.endsWith('.json') && f !== 'backup-context.json');
      
      const backups: BackupMetadata[] = [];
      
      for (const file of backupFiles) {
        try {
          const filePath = path.join(this.backupDir, file);
          const stats = await fs.stat(filePath);
          
          // Parse filename: resto-uuid_2024-08-12_1723456789.json
          const match = file.match(/^(resto-[^_]+)_(\d{4}-\d{2}-\d{2})_(\d+)\.json$/);
          if (match) {
            const [, dbName, date, timestampStr] = match;
            
            backups.push({
              timestamp: parseInt(timestampStr),
              date,
              dbName,
              size: stats.size,
              docCount: 0 // We'd need to read file to get this
            });
          }
        } catch (error) {
          console.warn(`⚠️ Skipping invalid backup file: ${file}`);
        }
      }
      
      return backups.sort((a, b) => b.timestamp - a.timestamp);
    } catch {
      return [];
    }
  }

  async cleanupOldBackups(): Promise<void> {
    try {
      const backups = await this.listLocalBackups();
      const now = Date.now();
      const toDelete: string[] = [];

      backups.forEach((backup, index) => {
        const ageInDays = (now - backup.timestamp) / (24 * 60 * 60 * 1000);
        
        let shouldKeep = false;
        
        if (ageInDays <= 3) {
          // Keep all from last 3 days
          shouldKeep = true;
        } else if (ageInDays <= 7) {
          // Keep every 2nd backup (days 4-7)
          shouldKeep = index % 2 === 0;
        } else if (ageInDays <= 14) {
          // Keep every 3rd backup (days 8-14)
          shouldKeep = index % 3 === 0;
        } else if (ageInDays <= 30) {
          // Keep weekly backups (days 15-30)
          shouldKeep = index % 7 === 0;
        }
        // Delete everything older than 30 days
        
        if (!shouldKeep) {
          const fileName = `${backup.dbName}_${backup.date}_${backup.timestamp}.json`;
          toDelete.push(fileName);
        }
      });

      // Delete files
      for (const fileName of toDelete) {
        try {
          await fs.unlink(path.join(this.backupDir, fileName));
          console.log(`🗑️ Deleted old backup: ${fileName}`);
        } catch (error) {
          console.warn(`⚠️ Failed to delete backup ${fileName}:`, error);
        }
      }

    } catch (error) {
      console.error('❌ Backup cleanup failed:', error);
    }
  }

  async restoreFromBackup(backupTimestamp: number, restaurantId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const dbName = `resto-${restaurantId}`;
      const backups = await this.listLocalBackups();
      const backup = backups.find(b => b.timestamp === backupTimestamp);
      
      if (!backup) {
        return { success: false, error: 'Backup not found' };
      }

      const backupFileName = `${backup.dbName}_${backup.date}_${backup.timestamp}.json`;
      const backupPath = path.join(this.backupDir, backupFileName);
      
      // Read backup data
      const backupData = JSON.parse(await fs.readFile(backupPath, 'utf8'));
      
      // Create safety backup of current DB
      const safetyResult = await this.createBackup(restaurantId);
      if (!safetyResult.success) {
        console.warn('⚠️ Failed to create safety backup before restore');
      }

      // Delete current database
      await fetch(`${this.couchUrl}/${dbName}`, { method: 'DELETE' });
      
      // Recreate database
      await fetch(`${this.couchUrl}/${dbName}`, { method: 'PUT' });
      
      // Restore documents
      const docs = backupData.rows?.map((row: any) => row.doc).filter((doc: any) => doc && !doc._id.startsWith('_design')) || [];
      
      if (docs.length > 0) {
        const bulkResponse = await fetch(`${this.couchUrl}/${dbName}/_bulk_docs`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ docs })
        });
        
        if (!bulkResponse.ok) {
          throw new Error(`Bulk restore failed: ${bulkResponse.status}`);
        }
      }

      console.log(`✅ Database restored from backup: ${backupFileName} (${docs.length} docs)`);
      return { success: true };

    } catch (error) {
      console.error('❌ Restore failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
}