'use client';

import { useState, useEffect } from 'react';
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Wifi, WifiOff, UserPlus } from 'lucide-react';
import { useAuth } from "@/lib/context/multi-user-auth-provider";
import { usePlatform } from "@/lib/context/platform-context";
import { useStaticNavigation } from '@/lib/utils/navigation';
import { isWebBuild } from '@/lib/utils/build-utils';
import { offlineApi } from '@/lib/api/offline-api-client';

interface LoginFormData {
  identifier: string;
  password: string;
}

interface RegisterFormData {
  ownerName: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword: string;
}

interface User {
  id: string;
  name: string;
  email?: string;
  role: string;
  restaurantId: string;
  permissions?: any;
  metadata?: any;
}

interface AuthReturnWithOffline {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (credentials: { identifier: string; password: string; restaurantId?: string; isStaffLogin?: boolean }) => Promise<boolean>;
  logout: () => void;
  register: (userData: { name: string; email: string; password: string; phoneNumber?: string }) => Promise<boolean>;
  isAdmin: boolean;
  isOwner: boolean;
  canManageStaff: boolean;
  offlineLogin: (role?: string) => Promise<boolean>;
  isOfflineMode: boolean;
}

function BistroLoginForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  const [activeTab, setActiveTab] = useState<'signin' | 'register'>('signin');
  const [isLoading, setIsLoading] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [error, setError] = useState<string>('');
  const [isClient, setIsClient] = useState(false);
  const [loginForm, setLoginForm] = useState<LoginFormData>({
    identifier: '',
    password: ''
  });
  const [registerForm, setRegisterForm] = useState<RegisterFormData>({
    ownerName: '',
    email: '',
    phoneNumber: '',
    password: '',
    confirmPassword: ''
  });

  // Static-compatible URL parameter handling
  const [urlParams, setUrlParams] = useState<URLSearchParams | null>(null);
  const { navigate } = useStaticNavigation();
  
  const auth = useAuth() as unknown as AuthReturnWithOffline;
  const { isAuthenticated, user, loading: authLoading, login, register: authRegister, offlineLogin } = auth;
  
  const { isStatic } = usePlatform();

  // Set isClient on mount and handle URL parameters
  useEffect(() => {
    setIsClient(true);
    
    // Static-compatible URL parameter handling
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      setUrlParams(params);
      
      // Handle URL parameters for tab switching
      const page = params.get('page');
      if (page === 'signin' || page === 'register') {
        setActiveTab(page);
      }
    }
  }, []);

  // Check connectivity status for static builds
  useEffect(() => {
    if (isStatic) {
      checkConnectivity();
      const interval = setInterval(checkConnectivity, 30000); // Check every 30 seconds
      return () => clearInterval(interval);
    }
  }, [isStatic]);

  // Handle authentication state changes - simplified to prevent redirect loops
  useEffect(() => {
    console.log('🔄 [Auth] State change:', { isClient, authLoading, isAuthenticated, user: !!user });
    
    if (!isClient) {
      console.log('⏳ [Auth] Waiting for client...');
      return;
    }

    // Don't redirect if still loading auth state
    if (authLoading) {
      console.log('⏳ [Auth] Auth still loading...');
      return;
    }

    // Check if we're in add user mode
    const mode = urlParams?.get('mode');
    const isAddUserMode = mode === 'add_user';
    
    if (isAuthenticated && user) {
      console.log('✅ [Auth] User authenticated, checking redirect...');
      
      // If we're in add user mode, check if this is a new user session
      if (isAddUserMode) {
        // Check if we have context about adding a user
        const contextStr = sessionStorage.getItem('temp_switch_user_context');
        if (contextStr) {
          try {
            const context = JSON.parse(contextStr);
            if (context.action === 'add_user' && context.currentUserId === user.id) {
              // Same user in add mode, stay on auth page
              console.log('🔄 [Auth] Same user in add mode, staying on auth page');
              return;
            }
          } catch (e) {
            console.error('❌ [Auth] Error parsing switch user context:', e);
          }
        }
      }
      
      // Check if we're already on a different page to prevent navigation loops
      const currentPath = window.location.pathname;
      const isOnAuthPage = currentPath.includes('/auth') || currentPath.endsWith('/auth/index.html');
      
      if (!isOnAuthPage) {
        console.log('🚫 [Auth] Already on non-auth page, not redirecting');
        return; // Already navigated away
      }
      
      // Prevent multiple redirects with a flag
      if (window.isRedirecting) {
        console.log('🚫 [Auth] Already redirecting, preventing duplicate redirect');
        return;
      }
      
      // Normal authentication flow - redirect after successful auth
      console.log('🏠 [Auth] Redirecting authenticated user...');
      window.isRedirecting = true;
      
      const timer = setTimeout(() => {
        // Get redirect URL from query params if available
        const redirectUrl = urlParams?.get('redirect');
        if (redirectUrl) {
          console.log('🔄 [Auth] Redirecting to:', redirectUrl);
          const cleanRedirectUrl = redirectUrl.replace(/^\//, '').replace(/\.html$/, '');
          navigate(cleanRedirectUrl);
          return;
        }
        // Use build-aware redirect logic with mobile detection
        let redirectTarget = 'menu'; // Default to menu
        
        if (isWebBuild()) {
          const userAgent = window.navigator.userAgent.toLowerCase();
          const isMobile = /mobile|android|iphone|ipad|tablet|phone/i.test(userAgent) || window.innerWidth <= 768;
          
          // Mobile users can access the app, desktop users go to landing
          redirectTarget = isMobile ? 'menu' : 'landing';
        }
        
        console.log(`🏠 [Auth] Redirecting to ${redirectTarget}`);
        navigate(redirectTarget);
      }, 800); // Longer delay to prevent race conditions

      return () => {
        clearTimeout(timer);
        window.isRedirecting = false;
      };
    } else {
      console.log('🔐 [Auth] User not authenticated, showing auth form');
      window.isRedirecting = false;
    }
  }, [isAuthenticated, user, navigate, authLoading, isClient, urlParams]);

  const checkConnectivity = async () => {
    if (isStatic) {
      const online = await offlineApi.recheckConnectivity();
      setIsOnline(online);
    }
  };

  const isEmailFormat = (identifier: string) => {
    return identifier.includes('@');
  };

  const handleSignIn = async () => {
    setIsLoading(true);
    setError('');

    try {
      const { identifier, password } = loginForm;
      
      if (!identifier.trim() || !password.trim()) {
        throw new Error('Please enter both username/email and password');
      }

      // For static builds, authenticate directly with bistro.icu server
      if (isStatic) {
        const isOwner = isEmailFormat(identifier);
        
        try {
          // Dynamic server detection - use localhost in dev, bistro.icu in production
          const serverUrl = typeof window !== 'undefined' && 
            (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') ?
            'http://localhost:3000' : 'https://bistro.icu';
            
          const authEndpoint = isOwner 
            ? `${serverUrl}/api/auth/login`
            : `${serverUrl}/api/auth/staff/login`;
            
          console.log('🌐 Auth page using endpoint:', authEndpoint);
            
          const response = await fetch(authEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Origin': 'https://bistro.icu',
              'User-Agent': 'Bistro-Electron-App/1.0'
            },
            mode: 'cors',
            credentials: 'omit',
            body: JSON.stringify({
              identifier: identifier.trim(),
              password: password.trim()
            })
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error('🚨 Auth failed:', {
              status: response.status,
              statusText: response.statusText,
              endpoint: authEndpoint,
              errorData
            });
            throw new Error(errorData.message || `Authentication failed (${response.status})`);
          }

          const authData = await response.json();
          
          // Cache successful authentication
          localStorage.setItem('auth_token', authData.token);
          localStorage.setItem('user_data', JSON.stringify(authData.user));
          
          // Cache credentials for offline use
          const credentialKey = isOwner ? 'cached_owner' : 'cached_staff';
          localStorage.setItem(credentialKey, JSON.stringify({
            identifier: identifier.trim(),
            type: isOwner ? 'owner' : 'staff',
            timestamp: Date.now()
          }));

          // Use local auth system to set user state
          const success = await login({
            identifier: identifier.trim(),
            password: password.trim(),
            isStaffLogin: !isOwner
          });

          if (!success) {
            throw new Error('Local authentication setup failed');
          }

        } catch (fetchError) {
          // Fallback to offline authentication if server is unreachable
          console.warn('Server authentication failed, trying offline:', fetchError);
          
          const cachedKey = isEmailFormat(loginForm.identifier) ? 'cached_owner' : 'cached_staff';
          const cached = localStorage.getItem(cachedKey);
          
          if (cached) {
            const cachedData = JSON.parse(cached);
            if (cachedData.identifier === loginForm.identifier.trim()) {
              // Use offline login
              const success = await offlineLogin(isEmailFormat(loginForm.identifier) ? 'owner' : 'staff');
              if (success) return;
            }
          }
          
          throw new Error('Authentication failed. Check your internet connection or credentials.');
        }
      } else {
        // Non-static builds use existing auth system
        const isOwner = isEmailFormat(identifier);
        
        const success = await login({
          identifier: identifier.trim(),
          password: password.trim(),
          isStaffLogin: !isOwner
        });
        
        if (!success) {
          throw new Error('Invalid credentials');
        }
      }

      // Redirect will be handled by useEffect
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = async () => {
    setIsLoading(true);
    setError('');

    try {
      const { ownerName, email, phoneNumber, password, confirmPassword } = registerForm;
      
      // Basic validation
      if (!ownerName.trim() || !email.trim() || !phoneNumber.trim() || !password.trim()) {
        throw new Error('Please fill in all fields');
      }
      
      if (password !== confirmPassword) {
        throw new Error('Passwords do not match');
      }
      
      if (password.length < 6) {
        throw new Error('Password must be at least 6 characters');
      }
      
      if (!email.includes('@')) {
        throw new Error('Please enter a valid email address');
      }

      // For static builds, register directly with bistro.icu server
      if (isStatic) {
        try {
          // Dynamic server detection - use localhost in dev, bistro.icu in production
          const serverUrl = typeof window !== 'undefined' && 
            (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') ?
            'http://localhost:3000' : 'https://bistro.icu';
            
          console.log('🌐 Auth page register using endpoint:', `${serverUrl}/api/auth/register`);
          
          const response = await fetch(`${serverUrl}/api/auth/register`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Origin': 'https://bistro.icu',
              'User-Agent': 'Bistro-Electron-App/1.0'
            },
            mode: 'cors',
            credentials: 'omit',
            body: JSON.stringify({
              name: ownerName.trim(),
              email: email.trim(),
              password: password.trim(),
              phoneNumber: phoneNumber.trim()
            })
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error('🚨 Registration failed:', {
              status: response.status,
              statusText: response.statusText,
              endpoint: `${serverUrl}/api/auth/register`,
              errorData
            });
            throw new Error(errorData.message || `Registration failed (${response.status})`);
          }

          const authData = await response.json();
          
          // Cache successful registration
          localStorage.setItem('auth_token', authData.token);
          localStorage.setItem('user_data', JSON.stringify(authData.user));
          
          // Cache owner credentials for offline use
          localStorage.setItem('cached_owner', JSON.stringify({
            identifier: email.trim(),
            type: 'owner',
            timestamp: Date.now()
          }));

          // Use local auth system to set user state
          const success = await authRegister({
            name: ownerName.trim(),
            email: email.trim(),
            password: password.trim(),
            phoneNumber: phoneNumber.trim()
          });

          if (!success) {
            throw new Error('Local registration setup failed');
          }

        } catch (fetchError) {
          console.error('Server registration failed:', fetchError);
          throw new Error('Registration failed. Check your internet connection.');
        }
      } else {
        // Non-static builds use existing auth system
        const success = await authRegister({
          name: ownerName.trim(),
          email: email.trim(),
          password: password.trim(),
          phoneNumber: phoneNumber.trim()
        });

        if (!success) {
          throw new Error('Registration failed. Please try again.');
        }

        // Cache owner credentials for offline use
        localStorage.setItem('cached_owner', JSON.stringify({
          identifier: email.trim(),
          type: 'owner',
          timestamp: Date.now()
        }));
      }

      // Redirect will be handled by useEffect
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Registration failed';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // If still loading auth state, show loading state
  if (!isClient || authLoading) {
    return (
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-sm text-muted-foreground">Loading...</p>
      </div>
    );
  }

  // Only show redirecting screen if we're actually authenticated and not in add user mode
  if (isAuthenticated && user) {
    const mode = urlParams?.get('mode');
    const isAddUserMode = mode === 'add_user';
    
    // If in add user mode, check if we should show the form or redirect
    if (isAddUserMode) {
      const contextStr = sessionStorage.getItem('temp_switch_user_context');
      if (contextStr) {
        try {
          const context = JSON.parse(contextStr);
          if (context.action === 'add_user' && context.currentUserId === user.id) {
            // Same user in add mode, show the auth form
            // Fall through to render the form
          } else {
            // Different context, show redirecting
            return (
              <div className="min-h-screen w-full flex items-center justify-center bg-background">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-sm text-muted-foreground">Redirecting...</p>
                </div>
              </div>
            );
          }
        } catch (e) {
          console.error('❌ [Auth] Error parsing switch user context:', e);
          // Fall through to show form
        }
      }
      // If no context or same user, show the form
    } else {
      // Normal authenticated mode - show redirecting
      return (
        <div className="min-h-screen w-full flex items-center justify-center bg-background">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-sm text-muted-foreground">Redirecting...</p>
          </div>
        </div>
      );
    }
  }

  const ConnectivityIndicator = () => {
    if (!isStatic) return null;
    
    return (
      <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
        {isOnline ? (
          <>
            <Wifi className="h-3.5 w-3.5 text-green-500" />
            <span>Online</span>
          </>
        ) : (
          <>
            <WifiOff className="h-3.5 w-3.5 text-amber-500" />
            <span>Offline Mode</span>
          </>
        )}
      </div>
    );
  };

  return (
    <div className={cn("flex flex-col gap-8 max-w-md md:max-w-lg lg:max-w-xl mx-auto", className)} {...props}>
      <form>
        <div className="flex flex-col gap-8">
          <div className="flex flex-col items-center gap-4 text-center">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight">Welcome to Bistro</h1>
            <div className="text-sm md:text-base text-muted-foreground">
              {activeTab === 'signin' ? (
                <>
                  Don&apos;t have an account?{" "}
                  <button 
                    type="button"
                    onClick={() => setActiveTab('register')}
                    className="underline underline-offset-4 hover:text-primary text-primary"
                    disabled={isLoading}
                  >
                    Sign up
                  </button>
                </>
              ) : (
                <>
                  Already have an account?{" "}
                  <button 
                    type="button"
                    onClick={() => setActiveTab('signin')}
                    className="underline underline-offset-4 hover:text-primary text-primary"
                    disabled={isLoading}
                  >
                    Sign in
                  </button>
                </>
              )}
            </div>
            
            {/* Add User Mode Indicator */}
            {urlParams?.get('mode') === 'add_user' && (
              <div className="inline-flex items-center gap-2 text-sm font-medium text-blue-600 bg-blue-50 dark:bg-blue-950/30 dark:text-blue-400 px-3 py-1.5 rounded-full border border-blue-200 dark:border-blue-800 mt-2">
                <UserPlus className="h-3.5 w-3.5" />
                <span>Adding New User</span>
              </div>
            )}
          </div>

          {/* Connectivity Indicator */}
          {isStatic && (
            <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
              {isOnline ? (
                <>
                  <Wifi className="h-3.5 w-3.5 text-green-500" />
                  <span>Online</span>
                </>
              ) : (
                <>
                  <WifiOff className="h-3.5 w-3.5 text-amber-500" />
                  <span>Offline Mode</span>
                </>
              )}
            </div>
          )}

          {/* Error Alert */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Sign In Form */}
          {activeTab === 'signin' && (
            <div className="flex flex-col gap-6 md:gap-8">
              <div className="grid gap-3">
                <Label htmlFor="identifier" className="text-sm md:text-base font-medium">Username or Email</Label>
                <Input
                  id="identifier"
                  type="text"
                  placeholder="Enter username or email"
                  value={loginForm.identifier}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, identifier: e.target.value }))}
                  disabled={isLoading}
                  required
                  className="h-10 md:h-12 text-sm md:text-base"
                />
                <p className="text-xs md:text-sm text-muted-foreground">
                  Use email for owners, username for staff
                </p>
              </div>
              <div className="grid gap-3">
                <Label htmlFor="password" className="text-sm md:text-base font-medium">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter password"
                  value={loginForm.password}
                  onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                  disabled={isLoading}
                  required
                  className="h-10 md:h-12 text-sm md:text-base"
                />
              </div>
              <Button type="button" className="w-full h-10 md:h-12 text-sm md:text-base" disabled={isLoading} onClick={handleSignIn}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  'Sign in'
                )}
              </Button>
            </div>
          )}

          {/* Register Form */}
          {activeTab === 'register' && (
            <div className="flex flex-col gap-6 md:gap-8">
              <div className="grid gap-4 md:gap-6">
                <div className="grid gap-3">
                  <Label htmlFor="phoneNumber" className="text-sm md:text-base font-medium">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    type="tel"
                    placeholder="Enter phone number"
                    value={registerForm.phoneNumber}
                    onChange={(e) => setRegisterForm(prev => ({ ...prev, phoneNumber: e.target.value }))}
                    disabled={isLoading}
                    required
                    className="h-10 md:h-12 text-sm md:text-base"
                  />
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="ownerName" className="text-sm md:text-base font-medium">Owner Name</Label>
                  <Input
                    id="ownerName"
                    placeholder="Enter your full name"
                    value={registerForm.ownerName}
                    onChange={(e) => setRegisterForm(prev => ({ ...prev, ownerName: e.target.value }))}
                    disabled={isLoading}
                    required
                    className="h-10 md:h-12 text-sm md:text-base"
                  />
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="email" className="text-sm md:text-base font-medium">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter email address"
                    value={registerForm.email}
                    onChange={(e) => setRegisterForm(prev => ({ ...prev, email: e.target.value }))}
                    disabled={isLoading}
                    required
                    className="h-10 md:h-12 text-sm md:text-base"
                  />
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="newPassword" className="text-sm md:text-base font-medium">Password</Label>
                  <Input
                    id="newPassword"
                    type="password"
                    placeholder="Create password (min 6 characters)"
                    value={registerForm.password}
                    onChange={(e) => setRegisterForm(prev => ({ ...prev, password: e.target.value }))}
                    disabled={isLoading}
                    required
                    className="h-10 md:h-12 text-sm md:text-base"
                  />
                </div>
                <div className="grid gap-3">
                  <Label htmlFor="confirmPassword" className="text-sm md:text-base font-medium">Confirm Password</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    placeholder="Confirm password"
                    value={registerForm.confirmPassword}
                    onChange={(e) => setRegisterForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    disabled={isLoading}
                    required
                    className="h-10 md:h-12 text-sm md:text-base"
                  />
                </div>
              </div>
              <Button type="button" className="w-full h-10 md:h-12 text-sm md:text-base" disabled={isLoading} onClick={handleRegister}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating account...
                  </>
                ) : (
                  'Create account'
                )}
              </Button>
            </div>
          )}

          {/* Offline Info */}
          {isStatic && !isOnline && (
            <div className="text-center text-xs text-muted-foreground">
              Running in offline mode. Some features may be limited.
            </div>
          )}
        </div>
      </form>
    </div>
  );
}

function AuthPageContent() {
  const auth = useAuth() as unknown as AuthReturnWithOffline;
  const { isAuthenticated, user, loading: authLoading } = auth;
  const { navigate } = useStaticNavigation();
  const [isClient, setIsClient] = useState(false);
  const [urlParams, setUrlParams] = useState<URLSearchParams | null>(null);

  useEffect(() => {
    setIsClient(true);
    
    // Static-compatible URL parameter handling
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);
      setUrlParams(params);
    }
  }, []);

  // Handle authentication redirects - simplified to prevent loops
  useEffect(() => {
    if (!isClient || authLoading) return;

    const mode = urlParams?.get('mode');
    const isAddUserMode = mode === 'add_user';
    
    if (isAuthenticated && user && !isAddUserMode) {
      // Only redirect for normal auth, not add user mode
      console.log('🔄 [AuthPageContent] Redirecting authenticated user...');
      
      const timer = setTimeout(() => {
        const redirectUrl = urlParams?.get('redirect');
        if (redirectUrl) {
          const cleanRedirectUrl = redirectUrl.replace(/^\//, '').replace(/\.html$/, '');
          navigate(cleanRedirectUrl);
          return;
        }
        // Use build-aware redirect logic with mobile detection
        let redirectTarget = 'menu'; // Default to menu
        
        if (isWebBuild()) {
          const userAgent = window.navigator.userAgent.toLowerCase();
          const isMobile = /mobile|android|iphone|ipad|tablet|phone/i.test(userAgent) || window.innerWidth <= 768;
          
          // Mobile users can access the app, desktop users go to landing
          redirectTarget = isMobile ? 'menu' : 'landing';
        }
        
        navigate(redirectTarget);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, user, navigate, authLoading, isClient, urlParams]);

  // Loading state
  if (!isClient || authLoading) {
    return (
      <div className="min-h-screen w-full flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Only show redirect state if we're actually redirecting
  if (isAuthenticated && user && !authLoading) {
    const mode = urlParams?.get('mode');
    const isAddUserMode = mode === 'add_user';
    
    if (!isAddUserMode) {
      return (
        <div className="min-h-screen w-full flex items-center justify-center bg-background">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-sm text-muted-foreground">Redirecting...</p>
          </div>
        </div>
      );
    }
  }

  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-background">
      <div className="w-full max-w-md md:max-w-lg lg:max-w-2xl xl:max-w-4xl mx-auto px-6 py-8">
        <BistroLoginForm />
      </div>
    </div>
  );
}

export default function AuthPage() {
  // Remove Suspense wrapper for static export compatibility
  return <AuthPageContent />;
}