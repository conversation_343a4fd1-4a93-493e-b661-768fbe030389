import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface BackupMetadata {
  timestamp: number;
  date: string;
  dbName: string;
  size: number;
  docCount: number;
}

interface BackupStatus {
  isRunning: boolean;
  lastBackup?: number;
  googleDriveConnected: boolean;
}

interface CloudBackupFile {
  id: string;
  name: string;
  createdTime: string;
  size: number;
}

export default function BackupSettings({ restaurantId }: { restaurantId: string }) {
  const [status, setStatus] = useState<BackupStatus>({ isRunning: false, googleDriveConnected: false });
  const [localBackups, setLocalBackups] = useState<BackupMetadata[]>([]);
  const [cloudBackups, setCloudBackups] = useState<CloudBackupFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [autoCloudBackup, setAutoCloudBackup] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);

  useEffect(() => {
    loadData();
  }, [restaurantId]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      
      // Load backup status
      const statusResponse = await fetch('/api/backup/status');
      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        setStatus(statusData);
        setAutoCloudBackup(statusData.googleDriveConnected);
      }

      // Load local backups
      const localResponse = await fetch('/api/backup/list-local');
      if (localResponse.ok) {
        const localData = await localResponse.json();
        setLocalBackups(localData.backups || []);
      }

      // Load cloud backups if connected
      if (status.googleDriveConnected) {
        const cloudResponse = await fetch('/api/backup/list-cloud');
        if (cloudResponse.ok) {
          const cloudData = await cloudResponse.json();
          setCloudBackups(cloudData.files || []);
        }
      }

    } catch (error) {
      console.error('Failed to load backup data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const connectGoogleDrive = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/backup/connect-drive', { method: 'POST' });
      const data = await response.json();
      
      if (data.success && data.authUrl) {
        // Open OAuth popup
        const popup = window.open(data.authUrl, 'google-oauth', 'width=500,height=600');
        
        // Listen for OAuth completion
        const handleMessage = (event: MessageEvent) => {
          if (event.data.type === 'GOOGLE_OAUTH_SUCCESS') {
            popup?.close();
            window.removeEventListener('message', handleMessage);
            setMessage({ type: 'success', text: 'Google Drive connected successfully!' });
            loadData();
          } else if (event.data.type === 'GOOGLE_OAUTH_ERROR') {
            popup?.close();
            window.removeEventListener('message', handleMessage);
            setMessage({ type: 'error', text: 'Failed to connect Google Drive' });
          }
        };
        
        window.addEventListener('message', handleMessage);
      } else {
        setMessage({ type: 'error', text: data.error || 'Failed to initiate Google Drive connection' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to connect Google Drive' });
    } finally {
      setIsLoading(false);
    }
  };

  const createBackup = async () => {
    try {
      setIsLoading(true);
      setMessage({ type: 'info', text: 'Creating backup...' });
      
      const response = await fetch('/api/backup/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ restaurantId })
      });
      
      const data = await response.json();
      
      if (data.success) {
        setMessage({ type: 'success', text: 'Backup created successfully!' });
        loadData();
      } else {
        setMessage({ type: 'error', text: data.error || 'Backup failed' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to create backup' });
    } finally {
      setIsLoading(false);
    }
  };

  const restoreBackup = async (timestamp: number, isCloud = false, fileId?: string) => {
    try {
      setIsLoading(true);
      setMessage({ type: 'info', text: 'Restoring backup...' });
      
      const endpoint = isCloud ? '/api/backup/restore-cloud' : '/api/backup/restore-local';
      const body = isCloud 
        ? { restaurantId, fileId }
        : { restaurantId, timestamp };
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      });
      
      const data = await response.json();
      
      if (data.success) {
        setMessage({ type: 'success', text: 'Database restored successfully! Please restart the app.' });
      } else {
        setMessage({ type: 'error', text: data.error || 'Restore failed' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to restore backup' });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleAutoCloudBackup = async (enabled: boolean) => {
    try {
      const response = await fetch('/api/backup/toggle-cloud', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled })
      });
      
      if (response.ok) {
        setAutoCloudBackup(enabled);
        setMessage({ 
          type: 'success', 
          text: enabled ? 'Auto cloud backup enabled' : 'Auto cloud backup disabled'
        });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to update cloud backup setting' });
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const formatRelativeTime = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const hours = Math.floor(diff / (60 * 60 * 1000));
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    return 'Less than 1 hour ago';
  };

  return (
    <div className="space-y-6">
      {message && (
        <Alert className={message.type === 'error' ? 'border-red-500' : message.type === 'success' ? 'border-green-500' : 'border-blue-500'}>
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Backup Status
            <Badge variant={status.isRunning ? "default" : "secondary"}>
              {status.isRunning ? "Active" : "Inactive"}
            </Badge>
          </CardTitle>
          <CardDescription>
            Automatic daily backups at 3 AM (with fallback times)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {status.lastBackup && (
              <div className="text-sm text-muted-foreground">
                Last backup: {formatRelativeTime(status.lastBackup)}
              </div>
            )}
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Google Drive Connected</span>
              <Badge variant={status.googleDriveConnected ? "default" : "outline"}>
                {status.googleDriveConnected ? "Connected" : "Not Connected"}
              </Badge>
            </div>
            
            {status.googleDriveConnected && (
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Auto Cloud Backup</span>
                <Switch
                  checked={autoCloudBackup}
                  onCheckedChange={toggleAutoCloudBackup}
                  disabled={isLoading}
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Backup Actions</CardTitle>
          <CardDescription>
            Create backups manually or connect Google Drive for cloud storage
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Button onClick={createBackup} disabled={isLoading}>
              Create Backup Now
            </Button>
            
            {!status.googleDriveConnected && (
              <Button variant="outline" onClick={connectGoogleDrive} disabled={isLoading}>
                Connect Google Drive
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Local Backups */}
      <Card>
        <CardHeader>
          <CardTitle>Local Backups</CardTitle>
          <CardDescription>
            Backups stored on this device
          </CardDescription>
        </CardHeader>
        <CardContent>
          {localBackups.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              No local backups found
            </div>
          ) : (
            <div className="space-y-2">
              {localBackups.slice(0, 10).map((backup) => (
                <div key={backup.timestamp} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium">{backup.date}</div>
                    <div className="text-sm text-muted-foreground">
                      {formatFileSize(backup.size)} • {backup.docCount} documents
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">
                      {formatRelativeTime(backup.timestamp)}
                    </span>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => restoreBackup(backup.timestamp)}
                      disabled={isLoading}
                    >
                      Restore
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Cloud Backups */}
      {status.googleDriveConnected && (
        <Card>
          <CardHeader>
            <CardTitle>Cloud Backups</CardTitle>
            <CardDescription>
              Backups stored in your Google Drive
            </CardDescription>
          </CardHeader>
          <CardContent>
            {cloudBackups.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                No cloud backups found
              </div>
            ) : (
              <div className="space-y-2">
                {cloudBackups.slice(0, 10).map((backup) => (
                  <div key={backup.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{backup.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {formatFileSize(backup.size)} • {new Date(backup.createdTime).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">
                        {formatRelativeTime(new Date(backup.createdTime).getTime())}
                      </span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => restoreBackup(0, true, backup.id)}
                        disabled={isLoading}
                      >
                        Restore
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}